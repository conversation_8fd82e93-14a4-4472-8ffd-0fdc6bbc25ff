[versions]
android-buildTools = "35.0.0"
android-sdk-min = "26"
android-sdk-target = "35"
android-sdk-compile = "35"

androidGradlePlugin = "8.9.3"

# Androidx
androidx-appCompat = "1.7.1"
androidx-constraintLayout = "2.2.1"
androidx-gridLayout = "1.1.0"
androidx-lifecycle = "2.9.1"
androidx-ktx = "1.16.0"
androidx-work = "2.10.1"
androidx-recyclerView = "1.4.0"
androidx-recyclerViewSelection = "1.2.0"
androidx-annotation = "1.9.1"
androidx-preference = "1.2.1"
androidx-securityCrypto = "1.0.0"
androidx-fragment = "1.8.8"
androidx-arch = "2.2.0"
androidx-compose-bom = "2025.06.00"
androidx-constraintLayoutCompose = "1.1.1"
androidx-navigation = "2.9.0"
androidx-activity = "1.10.1"
androidx-browser = "1.8.0"
androidx-media3 = "1.7.1"
androidx-room = "2.7.1"
androidx-credentials = "1.5.0"

android-flexbox = "3.0.0"
android-material = "1.12.0"

# Kotlin
kotlin = "2.1.20"
kotlinx-coroutines = "1.10.2"

# Testing
test-mockk = "1.14.2"
test-kluent = "1.73"
test-junit5-bom = "5.11.4"
turbine = "1.2.0"

# Common
dagger = "2.56.2"
dagger-hilt = "1.2.0"
gson = "2.13.1"
retrofit = "3.0.0"
okhttp = "4.12.0"
rudderstack = "1.27.2"
javax-annotation = "1.0"
javax-inject = "1"
glide = "4.16.0"
coil = "3.2.0"
libPhoneNumber = "9.0.7"
geocoder = "3.7"
twilio-voice = "6.9.0"
datadog = "2.22.0"
pusher = "2.4.4"
voyager = "1.1.0-beta03"
compose-unstyled = "1.31.1"
growthbook = "5.0.0"
growthbook-network-okhttp = "1.0.2"

# Firebase
firebase-bom = "33.15.0"
firebase-gradle = "5.0.0"
firebase-crashlytics-gradle = "3.0.4"
firebase-perf-plugin = "1.4.2"

#ML Kit
mlkit-doc-scanner = "16.0.0-beta1"

satismeter = "1.5.2"
play-app-update = "2.1.0"
play-services-ossLicenses = "17.1.0"
lokalise = "2.3.1-lite"
sqlCipher = "4.9.0"
apollo = "4.1.1"
jwtDecode = "2.0.2"
oss-licenses = "0.10.6"
google-services = "4.4.2"
json-path = "2.9.0"

# Tooling
leakCanary = "2.14"
soLoader = "0.12.1"
chucker = "4.1.0"
processPhoenix = "3.0.0"

# Codegen
ksp = "2.1.20-1.0.32"
kotlinPoet = "2.2.0"

# Custom gradle task
dom4j = "2.1.4"

# Google API
googleApiClient = "1.33.2"
googleAuthOAuth2 = "1.4.0"
googleApiSheets = "v4-rev20230815-2.0.0"
googleApiReporting = "v1beta1-rev20230803-2.0.0"
googleApiAndroidPublisher = "v3-rev20240610-2.0.0"

#Google Sign In
google-id = "1.1.1"

# BuildLogic
kover = "0.9.1"
jacobo = "2.1.0"
detekt = "1.23.7"
sonarqube = "6.2.0.5505"
ktlint = "12.3.0"
gradleVersion = "0.49.0"
slack = "1.45.3"
semver = "1.4.2"
gradleCacheFix = "3.0.1"

# Plugins
gradle-ktlint = "12.3.0"

[libraries]
# Android
android-plugin = { module = "com.android.tools.build:gradle", version.ref = "androidGradlePlugin" }
google-services = { module = "com.google.gms:google-services", version.ref = "google-services" }

# Kotlin
kotlin-stdlib = { module = "org.jetbrains.kotlin:kotlin-stdlib", version.ref = "kotlin" }
kotlin-plugin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlin" }

# KotlinX
kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "kotlinx-coroutines" }
kotlinx-coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "kotlinx-coroutines" }

# Dagger
dagger-compiler = { module = "com.google.dagger:dagger-compiler", version.ref = "dagger" }
dagger-core = { module = "com.google.dagger:dagger", version.ref = "dagger" }
dagger-hilt-gradlePlugin = { module = "com.google.dagger:hilt-android-gradle-plugin", version.ref = "dagger" }
dagger-hilt-android = { module = "com.google.dagger:hilt-android", version.ref = "dagger" }
dagger-hilt-androidCompiler = { module = "com.google.dagger:hilt-android-compiler", version.ref = "dagger" }
dagger-hilt-core = { module = "com.google.dagger:hilt-core", version.ref = "dagger" }

# AndroidX
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "androidx-ktx" }
androidx-hilt-work = { module = "androidx.hilt:hilt-work", version.ref = "dagger-hilt" }
androidx-hilt-compiler = { module = "androidx.hilt:hilt-compiler", version.ref = "dagger-hilt" }
androidx-preference = { module = "androidx.preference:preference", version.ref = "androidx-preference" }
androidx-encryptedPreferences = { module = "androidx.security:security-crypto", version.ref = "androidx-securityCrypto" }
androidx-media3-core = { module = "androidx.media3:media3-exoplayer", version.ref = "androidx-media3" }
androidx-media3-okhttp = { module = "androidx.media3:media3-datasource-okhttp", version.ref = "androidx-media3" }
androidx-annotation = { module = "androidx.annotation:annotation", version.ref = "androidx-annotation" }

# Arch
androidx-lifecycle-common = { module = "androidx.lifecycle:lifecycle-common", version.ref = "androidx-lifecycle" }
androidx-lifecycle-viewModel-ktx = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx", version.ref = "androidx-lifecycle" }
androidx-lifecycle-viewModel = { module = "androidx.lifecycle:lifecycle-viewmodel", version.ref = "androidx-lifecycle" }
androidx-lifecycle-viewModelSavedState = { module = "androidx.lifecycle:lifecycle-viewmodel-savedstate", version.ref = "androidx-lifecycle" }
androidx-lifecycle-scope = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "androidx-lifecycle" }
androidx-lifecycle-process = { module = "androidx.lifecycle:lifecycle-process", version.ref = "androidx-lifecycle" }
androidx-workManager = { module = "androidx.work:work-runtime", version.ref = "androidx-work" }
# Room
androidx-room-runtime = { module = "androidx.room:room-runtime", version.ref = "androidx-room" }
androidx-room-compiler = { module = "androidx.room:room-compiler", version.ref = "androidx-room" }
androidx-room-ktx = { module = "androidx.room:room-ktx", version.ref = "androidx-room" }
# UI
androidx-appCompat = { module = "androidx.appcompat:appcompat", version.ref = "androidx-appCompat" }
androidx-fragment = { module = "androidx.fragment:fragment-ktx", version.ref = "androidx-fragment" }
androidx-constraintLayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "androidx-constraintLayout" }
androidx-recyclerview = { module = "androidx.recyclerview:recyclerview", version.ref = "androidx-recyclerView" }
androidx-recyclerviewSelection = { module = "androidx.recyclerview:recyclerview-selection", version.ref = "androidx-recyclerViewSelection" }
androidx-gridLayout = { module = "androidx.gridlayout:gridlayout", version.ref = "androidx-gridLayout" }
androidx-browser = { module = "androidx.browser:browser", version.ref = "androidx-browser" }
androidx-activity-ktx = { module = "androidx.activity:activity-ktx", version.ref="androidx-activity" }
androidx-activity = { module = "androidx.activity:activity", version.ref = "androidx-activity" }
# Credentials
androidx-credentials = { module = "androidx.credentials:credentials", version.ref = "androidx-credentials" }
androidx-credentials-play-services-auth = { module = "androidx.credentials:credentials-play-services-auth", version.ref = "androidx-credentials" }

# Compose
androidx-compose-bom = { module = "androidx.compose:compose-bom", version.ref = "androidx-compose-bom" }
androidx-compose-ui-core = { module = "androidx.compose.ui:ui" }
androidx-compose-ui-graphics = { module = "androidx.compose.ui:ui-graphics" }
androidx-compose-ui-text = { module = "androidx.compose.ui:ui-text" }
androidx-compose-ui-unit = { module = "androidx.compose.ui:ui-unit" }
androidx-compose-ui-tooling = { module = "androidx.compose.ui:ui-tooling" }
androidx-compose-ui-util = { module = "androidx.compose.ui:ui-util" }
androidx-compose-foundation-core = { module = "androidx.compose.foundation:foundation" }
androidx-compose-foundation-layout = { module = "androidx.compose.foundation:foundation-layout" }
androidx-compose-animation-core = { module = "androidx.compose.animation:animation-core" }
androidx-compose-animation = { module = "androidx.compose.animation:animation" }
androidx-compose-material-icons = { module = "androidx.compose.material:material-icons-core" }
androidx-compose-material3 = { module = "androidx.compose.material3:material3" }
androidx-compose-preview = { module = "androidx.compose.ui:ui-tooling-preview" }
androidx-compose-debug = { module = "androidx.compose.ui:ui-tooling" }
androidx-compose-activity = { module = "androidx.activity:activity-compose", version.ref = "androidx-activity" }
androidx-compose-navigation = { module = "androidx.navigation:navigation-compose", version.ref = "androidx-navigation" }
androidx-compose-constraintLayout = { module = "androidx.constraintlayout:constraintlayout-compose", version.ref = "androidx-constraintLayoutCompose" }
androidx-compose-viewModel = { module = "androidx.lifecycle:lifecycle-viewmodel-compose", version.ref = "androidx-lifecycle" }
androidx-compose-runtime-lifecycle = { module = "androidx.lifecycle:lifecycle-runtime-compose", version.ref = "androidx-lifecycle" }
androidx-compose-runtime = { module = "androidx.compose.runtime:runtime" }
androidx-compose-runtime-saveable = { module = "androidx.compose.runtime:runtime-saveable" }
androidx-compose-runtime-livedata = { module = "androidx.compose.runtime:runtime-livedata" } #TODO Remove after full Compose migration
voyager-navigator = { module = "cafe.adriel.voyager:voyager-navigator", version.ref = "voyager" }
voyager-bottomSheetNavigator = { module = "cafe.adriel.voyager:voyager-bottom-sheet-navigator", version.ref = "voyager" }
voyager-lifecycle-kmp = { module = "cafe.adriel.voyager:voyager-lifecycle-kmp", version.ref = "voyager" }
voyager-core = { module = "cafe.adriel.voyager:voyager-core", version.ref = "voyager" }
compose-unstyled = { module = "com.composables:core", version.ref = "compose-unstyled" }

# Firebase
firebase-bom = { module = "com.google.firebase:firebase-bom", version.ref = "firebase-bom" }
firebase-cloudMessaging = { module = "com.google.firebase:firebase-messaging" }
firebase-coroutineSupport = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-play-services" }
firebase-analytics = { module = "com.google.firebase:firebase-analytics" }
firebase-crashlytics = { module = "com.google.firebase:firebase-crashlytics-ktx" }
firebase-crashlytics-ndk = { module = "com.google.firebase:firebase-crashlytics-ndk" }
firebase-crashlytics-gradle = { module = "com.google.firebase:firebase-crashlytics-gradle", version.ref = "firebase-crashlytics-gradle" }
firebase-remoteConfig = { module = "com.google.firebase:firebase-config-ktx" }
firebase-performance = { module = "com.google.firebase:firebase-perf" }

#ML Kit
mlkit-doc-scaner = { module = "com.google.android.gms:play-services-mlkit-document-scanner", version.ref = "mlkit-doc-scanner" }

javax-annotation = { module = "javax.annotation:jsr250-api", version.ref = "javax-annotation" }
javax-inject = { module = "javax.inject:javax.inject", version.ref = "javax-inject" }

play-app-update = { module = "com.google.android.play:app-update", version.ref = "play-app-update" }
play-services-ossLicenses = { module = "com.google.android.gms:play-services-oss-licenses", version.ref = "play-services-ossLicenses" }
oss-licenses-plugin = { module = "com.google.android.gms:oss-licenses-plugin", version.ref = "oss-licenses" }

lokalise = { module = "com.lokalise.android:sdk", version.ref = "lokalise" }

# UI
android-material = { module = "com.google.android.material:material", version.ref = "android-material" }
android-flexbox = { module = "com.google.android.flexbox:flexbox", version.ref = "android-flexbox" }
glide = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
glideKsp = { module = "com.github.bumptech.glide:ksp", version.ref = "glide" }
coil-compose = {module = "io.coil-kt.coil3:coil-compose", version.ref = "coil"}
coil-network-okttp = {module = "io.coil-kt.coil3:coil-network-okhttp", version.ref = "coil"}

# Test
test-junit5-bom = { module = "org.junit:junit-bom", version.ref = "test-junit5-bom" }
test-junit5-api = { module = "org.junit.jupiter:junit-jupiter-api" }
test-junit5-params = { module = "org.junit.jupiter:junit-jupiter-params" }
test-junit5-engine = { module = "org.junit.jupiter:junit-jupiter-engine" }
test-kotlinx-coroutinesTest = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-test", version.ref = "kotlinx-coroutines" }
test-androidx-archCoreTesting = { module = "androidx.arch.core:core-testing", version.ref = "androidx-arch" }
test-mockk-dsl = { module = "io.mockk:mockk-dsl", version.ref = "test-mockk" }
test-mockk = { module = "io.mockk:mockk", version.ref = "test-mockk" }
test-kluent-core = { module = "org.amshove.kluent:kluent", version.ref = "test-kluent" }
test-kluent-android = { module = "org.amshove.kluent:kluent-android", version.ref = "test-kluent" }
test-turbine = { module = "app.cash.turbine:turbine", version.ref = "turbine" }

# Services
twilio-voice = { module = "com.twilio:voice-android", version.ref = "twilio-voice" }
pusher-core = { module = "com.pusher:pusher-java-client", version.ref = "pusher" }
datadog = { module = "com.datadoghq:dd-sdk-android-logs", version.ref = "datadog" }
satismeter = { module = "com.satismeter:satismeter", version.ref = "satismeter" }
rudderstack = { module = "com.rudderstack.android.sdk:core", version.ref = "rudderstack" }
sqlCipher = { module = "net.zetetic:sqlcipher-android", version.ref = "sqlCipher" }
jwtDecode = { module = "com.auth0.android:jwtdecode", version.ref = "jwtDecode" }

# Http
rest-retrofit-core = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofit" }
rest-retrofit-gson = { module = "com.squareup.retrofit2:converter-gson", version.ref = "retrofit" }
rest-okhttp-core = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }
rest-okhttp-logging = { module = "com.squareup.okhttp3:logging-interceptor", version.ref = "okhttp" }

# Graphql
apollo-runtime = { module = "com.apollographql.apollo:apollo-runtime", version.ref = "apollo" }
apollo-plugin = { module = "com.apollographql.apollo:apollo-gradle-plugin", version.ref = "apollo" }

# Json
json-path = { module = "com.jayway.jsonpath:json-path", version.ref = "json-path" }
gson = { module = "com.google.code.gson:gson", version.ref = "gson" }

# GrowthBook
growthbook-sdk = { module = "io.growthbook.sdk:GrowthBook", version.ref = "growthbook" }
growthbook-network-okhttp = { module = "io.growthbook.sdk:NetworkDispatcherOkHttp", version.ref = "growthbook-network-okhttp" }

# Phone
libPhoneNumber = { module = "com.googlecode.libphonenumber:libphonenumber", version.ref = "libPhoneNumber" }
geocoder = { module = "com.googlecode.libphonenumber:geocoder", version.ref = "geocoder" }

# Tools
tools-chucker = { module = "com.github.chuckerteam.chucker:library", version.ref = "chucker" }
tools-leakCanary = { module = "com.squareup.leakcanary:leakcanary-android", version.ref = "leakCanary" }
tools-soLoader = { module = "com.facebook.soloader:soloader", version.ref = "soLoader" }
tools-processPhoenix = { module = "com.jakewharton:process-phoenix", version.ref = "processPhoenix" }

# Codegen
ksp-api = { module = "com.google.devtools.ksp:symbol-processing-api", version.ref = "ksp" }
ksp-kotlinPoet = { module = "com.squareup:kotlinpoet-ksp", version.ref = "kotlinPoet" }

# Custom gradle task
dom4j = { module = "org.dom4j:dom4j", version.ref = "dom4j" }

# Google API
google-apiClient = { module = "com.google.api-client:google-api-client", version.ref = "googleApiClient" }
google-authOAuth2 = { module = "com.google.auth:google-auth-library-oauth2-http", version.ref = "googleAuthOAuth2" }
google-apiSheets = { module = "com.google.apis:google-api-services-sheets", version.ref = "googleApiSheets" }

# Google Sign-In
google-id = { module = "com.google.android.libraries.identity.googleid:googleid", version.ref = "google-id" }

# Build logic
buildLogic-plugin-kotlin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlin" }
buildLogic-plugin-kover = { module = "org.jetbrains.kotlinx.kover:org.jetbrains.kotlinx.kover.gradle.plugin", version.ref = "kover" }
buildLogic-plugin-jacobo = { module = "gradle.plugin.com.kageiit:jacobo-plugin", version.ref = "jacobo" }
buildLogic-plugin-detekt = { module = "io.gitlab.arturbosch.detekt:detekt-gradle-plugin", version.ref = "detekt" }
buildLogic-plugin-sonar = { module = "org.sonarsource.scanner.gradle:sonarqube-gradle-plugin", version.ref = "sonarqube" }
buildLogic-plugin-ktlint = { module = "org.jlleitschuh.gradle.ktlint:org.jlleitschuh.gradle.ktlint.gradle.plugin", version.ref = "ktlint" }
buildLogic-plugin-gradleVersion = { module = "com.github.ben-manes:gradle-versions-plugin", version.ref = "gradleVersion" }
buildLogic-plugin-slack-core = { module = "com.slack.api:slack-api-client", version.ref = "slack" }
buildLogic-plugin-slack-kotlin-client = { module = "com.slack.api:slack-api-client-kotlin-extension", version.ref = "slack" }
buildLogic-plugin-slack-kotlin-model = { module = "com.slack.api:slack-api-model-kotlin-extension", version.ref = "slack" }
buildLogic-plugin-google-play-developer-reporting = { module = "com.google.apis:google-api-services-playdeveloperreporting", version.ref = "googleApiReporting" }
buildLogic-plugin-google-play-developer-android-publisher = { module = "com.google.apis:google-api-services-androidpublisher", version.ref = "googleApiAndroidPublisher" }
buildLogic-plugin-gradleCacheFix = { module = "org.gradle.android.cache-fix:org.gradle.android.cache-fix.gradle.plugin", version.ref = "gradleCacheFix" }
buildLogic-semver = { module = "io.github.z4kn4fein:semver", version.ref = "semver" }

# Twilio Debug app
voyager-hilt = { module = "cafe.adriel.voyager:voyager-hilt", version.ref = "voyager" }

# Audio
lame = {module = "com.github.naman14:TAndroidLame", version = "1.1"}

[plugins]
firebase-appDistribution = { id = "com.google.firebase.appdistribution", version.ref = "firebase-gradle" }
firebase-perf = { id = "com.google.firebase.firebase-perf", version.ref = "firebase-perf-plugin" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
sonarqube = { id = "org.sonarqube", version.ref = "sonarqube" }
kotlin-kover = { id = "org.jetbrains.kotlinx.kover", version.ref = "kover" }
kotlin-compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin"}
gradle-ktlint = { id = "org.jlleitschuh.gradle.ktlint", version.ref = "gradle-ktlint" }


[bundles]
androidUnitTest = ["test-mockk", "test-kluent-core", "test-kluent-android"]
compose = ["androidx-compose-preview", "androidx-compose-ui-core", "androidx-compose-foundation-core", "androidx-compose-foundation-layout"]

# Build logic
buildLogic-googleSheet = ["google-apiClient", "google-authOAuth2", "google-apiSheets"]
buildLogic-slack = ["buildLogic-plugin-slack-core", "buildLogic-plugin-slack-kotlin-client", "buildLogic-plugin-slack-kotlin-model"]
