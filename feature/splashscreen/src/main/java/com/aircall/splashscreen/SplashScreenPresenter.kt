package com.aircall.splashscreen

import com.aircall.core.android.call.EXTRA_ACCEPT_CALL
import com.aircall.core.android.call.EXTRA_INBOUND_CALL_INVITATION
import com.aircall.core.android.call.EXTRA_INBOUND_CALL_METADATA
import com.aircall.core.android.call.models.ParcelableCallInvitation
import com.aircall.core.android.mapper.CallMetadataMapper
import com.aircall.entity.Call
import com.aircall.entity.ICallInvitation
import com.aircall.entity.analytics.CallEventKey
import com.aircall.navigation.FLAG_CLEAR_WHOLE_BACKSTACK
import com.aircall.navigation.FLAG_FORCE_NEW_ACTIVITY
import com.aircall.navigation.FLAG_NO_DUPLICATE_ON_TOP
import com.aircall.navigation.IRouter
import com.aircall.navigation.routing.Destinations
import com.aircall.navigation.routing.Destinations.HOME
import com.aircall.navigation.routing.Destinations.MODAL_EDIT_CONTACT
import com.aircall.navigation.routing.Destinations.PEOPLE
import com.aircall.navigation.routing.Destinations.PERMISSIONS
import com.aircall.navigation.routing.Destinations.PERMISSIONS_RESTRICTED_APP
import com.aircall.navigation.routing.Destinations.SIGN_IN_FORM
import com.aircall.navigation.routing.Destinations.START_WORKSPACE_CONVERSATION
import com.aircall.navigation.routing.RoutingConstants
import com.aircall.navigation.routing.RoutingConstants.EXTRA_DIAL_MODE
import com.aircall.navigation.routing.RoutingConstants.EXTRA_NAVIGATE_TO
import com.aircall.navigation.routing.RoutingConstants.EXTRA_PHONE_NUMBER
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

@ViewModelScoped
class SplashScreenPresenter @Inject constructor(
    private val router: IRouter,
    private val metadataMapper: CallMetadataMapper,
) : ISplashScreenPresenter {

    override var view: SplashScreenView? = null

    override fun redirectToHome() {
        router.navigateTo(HOME)
    }

    override fun redirectToDialer(preselectedPhoneNumber: String, dialingMode: CallEventKey.DialingMode?) {
        val extras = buildMap {
            put(EXTRA_NAVIGATE_TO, START_WORKSPACE_CONVERSATION)
            put(EXTRA_PHONE_NUMBER, preselectedPhoneNumber)
            dialingMode?.let {
                put(EXTRA_DIAL_MODE, dialingMode)
            }
        }

        router.navigateTo(
            destination = HOME,
            extras = extras,
            flags = FLAG_CLEAR_WHOLE_BACKSTACK,
        )
    }

    override fun redirectToLogin() {
        router.navigateTo(SIGN_IN_FORM)
    }

    override fun showSessionError() {
        router.navigateTo(SIGN_IN_FORM)
    }

    override fun redirectToPermissions() {
        router.navigateTo(PERMISSIONS)
    }

    override fun redirectToRestrictedApp() {
        router.navigateTo(PERMISSIONS_RESTRICTED_APP)
    }

    override fun redirectToAddContact() {
        router.navigateTo(
            destination = HOME,
            extras = mapOf(EXTRA_NAVIGATE_TO to PEOPLE),
            nextDestination = MODAL_EDIT_CONTACT,
        )
    }

    override fun redirectToSettings() {
        router.navigateTo(
            destination = HOME,
            extras = mapOf(RoutingConstants.EXTRA_OPEN_SETTINGS_PANEL to true),
        )
    }

    override fun redirectToRinging(callInvitation: ICallInvitation, call: Call) {
        router.navigateTo(
            destination = Destinations.IN_CALL,
            extras = mapOf(
                EXTRA_INBOUND_CALL_INVITATION to callInvitation as ParcelableCallInvitation,
                EXTRA_INBOUND_CALL_METADATA to metadataMapper.entityToParcelable(call),
                EXTRA_ACCEPT_CALL to false,
            ),
            flags = FLAG_NO_DUPLICATE_ON_TOP or FLAG_FORCE_NEW_ACTIVITY,
        )
    }
}
