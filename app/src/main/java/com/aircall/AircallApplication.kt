package com.aircall

import android.app.Application
import androidx.annotation.CallSuper
import androidx.hilt.work.HiltWorkerFactory
import androidx.work.Configuration
import com.aircall.application.IApplicationController
import com.aircall.core.coroutine.IApplicationScope
import com.aircall.core.logger.ILogger
import com.aircall.navigation.routing.NavigationRegistry
import com.aircall.repository.service.IGrowthBookService
import com.aircall.service.voice.twilio.TwilioLogger
import com.aircall.tooling.ITooling
import com.datadog.android.Datadog
import com.datadog.android.log.Logs
import com.datadog.android.log.LogsConfiguration
import com.datadog.android.privacy.TrackingConsent
import com.lokalise.sdk.Lokalise
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject
import androidx.work.Configuration as WorkManagerConfiguration
import com.datadog.android.core.configuration.Configuration as DatadogConfiguration

@HiltAndroidApp
open class AircallApplication : Application(), WorkManagerConfiguration.Provider {

    @Inject
    lateinit var controller: IApplicationController

    @Inject
    lateinit var tooling: ITooling

    // Used for Worker Hilt injection
    @Inject
    lateinit var workerFactory: HiltWorkerFactory

    @Inject
    lateinit var logger: ILogger

    @Inject
    lateinit var twilioLogger: TwilioLogger

    @Inject
    lateinit var registry: NavigationRegistry

    @Inject
    lateinit var growthBookService: IGrowthBookService

    @Inject
    lateinit var appScope: IApplicationScope

    override fun onCreate() {
        setupBeforeHiltInjection()
        super.onCreate()
        onApplicationDependenciesInjected()
    }

    @CallSuper
    open fun onApplicationDependenciesInjected() {
        tooling.init()

        Lokalise.init(
            appContext = this,
            sdkToken = BuildConfig.LOKALISE_API_TOKEN,
            projectId = BuildConfig.LOKALISE_PROJECT_ID,
        )

        twilioLogger.init()

        registry.initNavigation()

//        appScope.launch { TODO AND-9227 Make use of GrowthBook
//            growthBookService.init(emptyMap())
//        }

        controller.onApplicationCreated()
    }

    // Used for Worker Hilt injection
    override val workManagerConfiguration: Configuration
        get() = WorkManagerConfiguration.Builder()
            .setWorkerFactory(workerFactory)
            .build()

    private fun setupBeforeHiltInjection() {
        val config = DatadogConfiguration.Builder(
            clientToken = BuildConfig.DATADOG_API_KEY,
            env = BuildConfig.BUILD_TYPE,
            variant = BuildConfig.FLAVOR,
        ).build()
        Datadog.initialize(this, config, TrackingConsent.GRANTED)

        val logsConfig = LogsConfiguration.Builder().build()
        Logs.enable(logsConfig)
    }
}
