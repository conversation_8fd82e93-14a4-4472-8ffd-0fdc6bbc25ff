package com.aircall.service.growthbook

import com.aircall.core.annotation.config.Config
import com.aircall.core.annotation.network.apikey.ApiKeys
import com.aircall.core.logger.GROWTHBOOK
import com.aircall.core.logger.ILogger
import com.aircall.repository.service.IGrowthBookService
import com.sdk.growthbook.GBSDKBuilder
import com.sdk.growthbook.GrowthBookSDK
import com.sdk.growthbook.model.GBValue
import com.sdk.growthbook.model.toGbBoolean
import com.sdk.growthbook.model.toGbNumber
import com.sdk.growthbook.model.toGbString
import com.sdk.growthbook.network.GBNetworkDispatcherOkHttp
import com.sdk.growthbook.utils.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class GrowthBookService @Inject constructor(
    @ApiKeys.GrowthBook private val apiKey: String,
    @Config.GrowthBookHostUrl private val hostUrl: String,
    private val logger: ILogger,
) : IGrowthBookService {

    private lateinit var growthBookSdk: GrowthBookSDK

    override suspend fun init(userAttributes: Map<String, Any>) {
        val builder = GBSDKBuilder(
            apiKey = apiKey,
            hostURL = hostUrl,
            attributes = convertToGBValueMap(userAttributes).toMutableMap(),
            trackingCallback = { _, _ -> },
            networkDispatcher = GBNetworkDispatcherOkHttp(),
        )
        growthBookSdk = builder.initialize()
    }

    override fun isFeatureEnabled(featureKey: String): Boolean {
        return if (!isReady()) {
            logger.d(msg = "GrowthBook was not ready when checking feature $featureKey", tags = setOf(GROWTHBOOK))
            false
        } else {
            growthBookSdk.isOn(featureKey)
        }
    }

    override fun isReady(): Boolean = ::growthBookSdk.isInitialized

    private fun autoRefreshFeatures(): Flow<Boolean> { // TODO AND-9227: Check that this works, right now streaming updates is disabled
        return if (::growthBookSdk.isInitialized) {
            growthBookSdk.autoRefreshFeatures()
                .map { resource ->
                    when (resource) {
                        is Resource.Success -> {
                            logger.d(msg = "Auto-refresh success", tags = setOf(GROWTHBOOK))
                            true
                        }

                        is Resource.Error -> {
                            logger.e(msg = "Auto-refresh failure: ${resource.exception.message}", tags = setOf(GROWTHBOOK))
                            false
                        }
                    }
                }
        } else {
            flowOf(false)
        }
    }

    private fun convertToGBValueMap(attributes: Map<String, Any>): Map<String, GBValue> {
        return attributes.mapValues { (_, value) ->
            when (value) {
                is String -> value.toGbString()
                is Boolean -> value.toGbBoolean()
                is Number -> value.toGbNumber()
                else -> value.toString().toGbString()
            }
        }
    }
}
