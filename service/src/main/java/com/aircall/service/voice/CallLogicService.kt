package com.aircall.service.voice

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import androidx.annotation.VisibleForTesting
import androidx.core.os.BundleCompat
import com.aircall.core.android.call.EXTRA_CONFERENCE_CALL
import com.aircall.core.android.call.EXTRA_INBOUND_CALL_INVITATION
import com.aircall.core.android.call.models.ParcelableCallInvitation
import com.aircall.core.android.call.models.TELECOM_INVITE_KEY
import com.aircall.core.extensions.getMySelfInParticipant
import com.aircall.core.logger.ILogger
import com.aircall.entity.Answer
import com.aircall.entity.Call
import com.aircall.entity.FailureReason
import com.aircall.entity.ICallInvitation
import com.aircall.entity.Participant
import com.aircall.entity.User
import com.aircall.entity.analytics.LogType
import com.aircall.entity.failure
import com.aircall.entity.success
import com.aircall.repository.log.Log.Event.TWILIO
import com.aircall.repository.log.Log.Event.TWILIO_REGISTRATION_FAILURE
import com.aircall.repository.log.Log.Event.TWILIO_REGISTRATION_SUCCESS
import com.aircall.repository.service.IInstanceIdProvider
import com.aircall.repository.service.IMonitoringService
import com.aircall.repository.service.call.ICallLogicService
import com.aircall.service.provider.ITwilioCallProvider
import com.aircall.service.voice.taskremoval.TaskRemovalDetectorHandlerService
import com.aircall.service.voice.twilio.TwilioCallHandlerService
import com.aircall.service.voice.twilio.TwilioCallHandlerService.Companion.EXTRA_EXTERNAL_OUTBOUND_CALL
import com.aircall.service.voice.twilio.TwilioCallHandlerService.Companion.EXTRA_INCOMING_CALL_INVITATION
import com.aircall.service.voice.twilio.TwilioCallHandlerService.Companion.EXTRA_INTERNAL_OUTBOUND_CALL
import com.twilio.voice.CallInvite
import com.twilio.voice.Voice
import dagger.hilt.android.qualifiers.ApplicationContext
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject
import javax.inject.Singleton
import com.aircall.translation.R as RTranslation

@Singleton
class CallLogicService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val callProvider: ITwilioCallProvider,
    private val instanceIdProvider: IInstanceIdProvider,
    private val monitoring: IMonitoringService,
    private val logger: ILogger,
) : ICallLogicService {

    @VisibleForTesting
    var twilioCallHandlerService: TwilioCallHandlerService? = null

    @VisibleForTesting
    var isBound: AtomicBoolean = AtomicBoolean(false)

    @VisibleForTesting
    var serviceConnection: ServiceConnection? = null

    override suspend fun getCurrentUserInParticipant(userId: Int, call: Call): Answer<Participant> {
        return instanceIdProvider.getDeviceId()?.let { deviceId ->
            call.getMySelfInParticipant(userId, deviceId)?.let(::success) ?: run {
                failure(
                    error = NoSuchElementException(),
                    message = "No participant with $userId",
                    type = FailureReason.CACHE,
                )
            }
        } ?: failure(
            error = NoSuchElementException(),
            message = "Unable to retrieve device id will searching participant (id: $userId)",
            type = FailureReason.CACHE,
        )
    }

    override suspend fun registerNewCallAuthToken(user: User?, callAuthToken: String) {
        instanceIdProvider.getPushToken()?.let { pushToken ->
            registerPushToken(user, callAuthToken, pushToken)
        }
    }

    override fun registerPushToken(user: User?, callAuthToken: String, pushToken: String) {
        callProvider.register(
            accessToken = callAuthToken,
            registrationChannel = Voice.RegistrationChannel.FCM,
            registrationToken = pushToken,
            onRegistered = { _, _ ->
                monitoring.logTwilioEvent(user, LogType.INFO, TWILIO + TWILIO_REGISTRATION_SUCCESS)
                logger.d("Successfully registered FCM")
            },
            onError = { e, _, _ ->
                monitoring.logTwilioEvent(
                    user = user,
                    type = LogType.ERROR,
                    event = TWILIO + TWILIO_REGISTRATION_FAILURE,
                    message = "Registration Error ${e.errorCode}, ${e.message} because ${e.explanation}",
                )
                logger.e(
                    "registerPushToken",
                    Throwable("Registration Error: ${e.errorCode}, ${e.message} because ${e.explanation}"),
                )
            },
        )
    }

    override fun unRegisterPushToken(callAuthToken: String, pushToken: String) {
        callProvider.unregister(callAuthToken, Voice.RegistrationChannel.FCM, pushToken, { _, _ ->
            logger.d("Successfully unregistered FCM")
        }, { e, _, _ ->
            logger.e("Unregistration Error: ${e.errorCode}, ${e.message}")
        })
    }

    @VisibleForTesting
    fun createServiceConnection() = object : ServiceConnection {
        override fun onServiceDisconnected(p0: ComponentName?) {
            logger.d("Connection to service lost")
            twilioCallHandlerService = null
        }

        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            logger.d("Service connected")

            val binder = service as (TwilioCallHandlerService.LocalBinder)
            twilioCallHandlerService = binder.service
        }
    }

    override fun disconnectCall() {
        val twilioHandlerServiceIntent = Intent(context, TwilioCallHandlerService::class.java)
        val taskRemovalDetectorService = Intent(context, TaskRemovalDetectorHandlerService::class.java)

        context.stopService(twilioHandlerServiceIntent)
        context.stopService(taskRemovalDetectorService)
        doUnBindService()
    }

    override suspend fun acceptIncomingConferenceCall(callInvitation: ICallInvitation) {
        val intent = Intent(context, TwilioCallHandlerService::class.java)
        val boundServiceIntent = Intent(context, TwilioCallHandlerService::class.java)

        intent.putExtra(EXTRA_INCOMING_CALL_INVITATION, callInvitation as ParcelableCallInvitation)
        intent.putExtra(EXTRA_CONFERENCE_CALL, true)

        context.startService(intent)
        doBindService(boundServiceIntent)
    }

    override suspend fun acceptIncomingCall(callInvitation: ICallInvitation) {
        val intent = Intent(context, TwilioCallHandlerService::class.java)
        val boundServiceIntent = Intent(context, TwilioCallHandlerService::class.java)

        intent.putExtra(EXTRA_INCOMING_CALL_INVITATION, callInvitation as ParcelableCallInvitation)

        context.startService(intent)
        doBindService(boundServiceIntent)
    }

    private fun doBindService(boundServiceIntent: Intent) {
        doUnBindService()
        serviceConnection = createServiceConnection()
        context.bindService(boundServiceIntent, requireNotNull(serviceConnection), Context.BIND_AUTO_CREATE)
        isBound = AtomicBoolean(true)
    }

    @VisibleForTesting
    fun doUnBindService() {
        if (isBound.get()) {
            isBound = AtomicBoolean(false)
            context.unbindService(requireNotNull(serviceConnection))
        }
    }

    override fun declineIncomingCall(callInvitation: ICallInvitation) {
        val invite = (callInvitation as ParcelableCallInvitation).telecomInvite.let { bundle ->
            BundleCompat.getParcelable(bundle, TELECOM_INVITE_KEY, CallInvite::class.java)
        }

        callProvider.reject(invite)

        val taskRemovalDetectorService = Intent(context, TaskRemovalDetectorHandlerService::class.java)
        context.stopService(taskRemovalDetectorService)
    }

    override suspend fun startExternalOutboundCall(callAuthToken: String, callerId: String, phoneNumber: String) {
        startTaskRemovalDetectorService()

        val intent = Intent(context, TwilioCallHandlerService::class.java)
        val boundIntent = Intent(context, TwilioCallHandlerService::class.java)

        intent.putExtra(
            EXTRA_EXTERNAL_OUTBOUND_CALL,
            ExternalOutboundCallInfo(callAuthToken, callerId, phoneNumber, phoneNumber),
        )

        context.startService(intent)
        doBindService(boundIntent)
    }

    override suspend fun startInternalOutboundCall(callAuthToken: String, callerId: String, teammateId: String) {
        startTaskRemovalDetectorService()

        val intent = Intent(context, TwilioCallHandlerService::class.java)
        val boundIntent = Intent(context, TwilioCallHandlerService::class.java)

        intent.putExtra(
            EXTRA_INTERNAL_OUTBOUND_CALL,
            InternalOutboundCallInfo(
                callAuthToken,
                callerId,
                teammateId,
                context.getString(RTranslation.string.inCallTeammates),
            ),
        )

        context.startService(intent)
        doBindService(boundIntent)
    }

    override fun startTaskRemovalDetectorService(callInvitation: ICallInvitation?) {
        val intent = Intent(context, TaskRemovalDetectorHandlerService::class.java)

        callInvitation?.let {
            intent.putExtra(
                EXTRA_INBOUND_CALL_INVITATION,
                (it as ParcelableCallInvitation),
            )
        }

        context.startService(intent)
    }

    override suspend fun hangUpCall() {
        twilioCallHandlerService?.hangUpCall()
    }

    // Call action
    override fun mute() {
        twilioCallHandlerService?.muteCall()
    }

    override fun unmute() {
        twilioCallHandlerService?.unmuteCall()
    }

    override fun sendDigit(number: String) {
        twilioCallHandlerService?.sendDigit(number)
    }
}
