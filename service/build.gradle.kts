plugins {
    id("aircall.android.library")
    id("aircall.android.unit.test")
    id("com.google.devtools.ksp")
    id("kotlin-parcelize")
    id("dagger.hilt.android.plugin")
}

android {
    namespace = "com.aircall.service"

    testOptions {
        kotlin {
            compilerOptions {
                freeCompilerArgs.add("-java-parameters")
            }
        }
    }
}

dependencies {
    implementation(libs.androidx.appCompat)
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.scope)
    implementation(libs.androidx.workManager)
    implementation(libs.kotlin.stdlib)
    implementation(libs.kotlinx.coroutines.core)
    implementation(libs.kotlinx.coroutines.android)
    implementation(libs.play.app.update)

    // Dagger
    implementation(libs.dagger.core)
    implementation(libs.dagger.hilt.android)
    implementation(libs.androidx.hilt.work)
    ksp(libs.dagger.compiler)
    ksp(libs.dagger.hilt.androidCompiler)
    ksp(libs.androidx.hilt.compiler)

    // Twilio
    api(libs.twilio.voice)
    api(project(":audioflow"))

    // Firebase
    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.analytics) {
        exclude(group = "com.google.android.gms", module = "play-services-ads-identifier")
        exclude(group = "com.google.android.gms", module = "play-services-measurement")
        exclude(group = "com.google.android.gms", module = "play-services-measurement-sdk")
    }
    implementation(libs.firebase.cloudMessaging)
    implementation(libs.firebase.remoteConfig)
    implementation(libs.firebase.coroutineSupport)
    implementation(libs.firebase.performance)

    // Pusher
    implementation(libs.pusher.core)

    // LibPhoneNumber
    implementation(libs.libPhoneNumber)
    implementation(libs.geocoder)

    // Satismeter
    implementation(libs.satismeter) {
        exclude(group = "com.google.android.play", module = "core")
    }

    implementation(libs.jwtDecode)

    // GrowthBook
    implementation(libs.growthbook.sdk)
    implementation(libs.growthbook.network.okhttp)

    implementation(project(":entity"))
    implementation(project(":domain"))
    implementation(project(":repository"))
    implementation(project(":core"))
    implementation(project(":core:android"))
    implementation(project(":navigation"))
    implementation(project(":navigation:routing"))
    implementation(project(":translation"))
    implementation(project(":service:common"))

    // Lokalise
    implementation(libs.lokalise) {
        isTransitive = true
    }

    // Google SSO
    implementation(libs.google.id)
    implementation(libs.androidx.credentials)
    implementation(libs.androidx.credentials.play.services.auth)
    // test
    testImplementation(project(":core:test"))
    testImplementation(project(":core:test:coroutine"))
    testImplementation(libs.test.turbine)
    testImplementation(libs.test.junit5.params)
    testImplementation(libs.bundles.androidUnitTest)
    testImplementation(libs.test.kotlinx.coroutinesTest)
}
